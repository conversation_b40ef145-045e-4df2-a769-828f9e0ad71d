import axios from 'axios';

/**
 * Minimal demo: open Arc Browser via MCP server and list the returned UI elements count.
 *
 * Usage:
 *   npx tsx scripts/demos/_01_open_arc.ts
 */
async function main() {
  const mcpUrl = 'http://localhost:8080/mcp';

  console.log('[demo] initializing MCP session...');
  await axios.post(mcpUrl, {
    jsonrpc: '2.0',
    id: 'init-arc-demo',
    method: 'initialize',
    params: {
      clientInfo: { name: 'demo-script', version: '0.1' },
      capabilities: {}
    }
  });
  console.log('[demo] initialization complete');

  console.log('[demo] sending openApplication("Arc")...');
  const response = await axios.post(mcpUrl, {
    jsonrpc: '2.0',
    id: 'open-arc',
    method: 'executeToolFunction',
    params: {
      function: 'openApplication',
      arguments: { app_name: 'Arc' }
    }
  });

  const result = response.data?.result;
  if (!result) {
    console.error('[demo] unexpected response:', response.data);
    return;
  }

  console.log(`[demo] Arc opened. Elements returned: ${result.elements?.length ?? 'unknown'}`);
  console.log('[demo] first 5 elements:', (result.elements || []).slice(0, 5));
}

main().catch(err => {
  console.error('[demo] error:', err.message || err);
  process.exit(1);
}); 