#!/usr/bin/env python3
"""
Generate a basic Zsh completion script for any CLI by parsing its --help output.
"""
import os
import re
import subprocess
from dataclasses import dataclass
from typing import List, Optional

#----- Data Structures -----
@dataclass
class CLIOption:
    short: Optional[str]
    long: Optional[str]
    description: str

@dataclass
class CLISubcommand:
    name: str
    description: str

#----- Helpers -----
def _run_help(cmd: str) -> str:
    try:
        result = subprocess.run([cmd, '--help'], capture_output=True, text=True, check=True)
        return result.stdout
    except Exception as e:
        print(f"Error running '{cmd} --help': {e}")
        exit(1)

def _parse_options(help_text: str) -> List[CLIOption]:
    options = []
    # Match lines like:   -h, --help   Show help message
    option_re = re.compile(r"^\s*(-\w)?(?:,\s*)?(--[\w-]+)?\s+(.*)$")
    for line in help_text.splitlines():
        m = option_re.match(line)
        if m:
            short, long, desc = m.groups()
            options.append(CLIOption(short, long, desc.strip()))
    return options

def _parse_subcommands(help_text: str) -> List[CLISubcommand]:
    subcommands = []
    # Look for a 'Commands:' or 'Available commands:' section
    in_cmd_section = False
    for line in help_text.splitlines():
        if re.match(r"^\s*(Commands|Available commands)\s*:?", line, re.I):
            in_cmd_section = True
            continue
        if in_cmd_section:
            if not line.strip():
                break
            m = re.match(r"^\s*(\w[\w-]*)\s+(.*)$", line)
            if m:
                name, desc = m.groups()
                subcommands.append(CLISubcommand(name, desc.strip()))
    return subcommands

def _zsh_completion_func(cmd: str, options: List[CLIOption], subcommands: List[CLISubcommand]) -> str:
    func_name = f"_{cmd}"
    lines = [f"#compdef {cmd}", f"\nfunction {func_name}() {{"]
    if subcommands:
        lines.append("  local -a subcmds")
        subcmds_str = ' '.join([f"'{s.name}:{s.description}'" for s in subcommands])
        lines.append(f"  subcmds=({subcmds_str})")
        lines.append("  _arguments '1: :->subcmds' && return 0")
        lines.append("  case $state in")
        lines.append("    subcmds)")
        lines.append("      _describe 'command' subcmds")
        lines.append("      ;;")
        lines.append("  esac")
    if options:
        for opt in options:
            opts = []
            if opt.short:
                opts.append(opt.short)
            if opt.long:
                opts.append(opt.long)
            opt_str = ','.join(opts)
            lines.append(f"  _arguments '{opt_str}[{opt.description}]'")
    lines.append("}")
    lines.append(f"{func_name}")
    return '\n'.join(lines)

def _save_completion(cmd: str, script: str) -> str:
    comp_dir = os.path.expanduser('~/.zsh/completions')
    os.makedirs(comp_dir, exist_ok=True)
    path = os.path.join(comp_dir, f'_{cmd}')
    with open(path, 'w') as f:
        f.write(script)
    return path

#----- Main Entrypoint -----
def main():
    import sys
    if len(sys.argv) < 2:
        print("Usage: python gen_zsh_completion.py <command>")
        exit(1)
    cmd = sys.argv[1]
    help_text = _run_help(cmd)
    options = _parse_options(help_text)
    subcommands = _parse_subcommands(help_text)
    script = _zsh_completion_func(cmd, options, subcommands)
    path = _save_completion(cmd, script)
    print(f"Zsh completion script generated: {path}")
    print(f"To enable, add this to your ~/.zshrc if not present:")
    print(f'  fpath=(~/.zsh/completions $fpath)')
    print(f'  autoload -Uz compinit && compinit')
    print(f"Then restart your shell or run: source ~/.zshrc")

if __name__ == "__main__":
    main()


