#!/usr/bin/env python3
"""
Instagram Browser Controller

A controller for automating Instagram interactions in a real browser using the 
Computer Use AI SDK MCP server.

Features:
- Open Instagram and login
- Navigate posts, stories, profiles
- Like, comment, follow/unfollow
- Search users and hashtags
- Send direct messages
- View and interact with stories

Usage:
    python instagram_controller.py
"""

import os
import time
import json
import requests
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum


class InstagramAction(Enum):
    """Available Instagram actions"""
    OPEN_INSTAGRAM = "open_instagram"
    LOGIN = "login"
    SEARCH_USER = "search_user"
    SEARCH_HASHTAG = "search_hashtag"
    LIKE_POST = "like_post"
    COMMENT_POST = "comment_post"
    FOLLOW_USER = "follow_user"
    UNFOLLOW_USER = "unfollow_user"
    VIEW_STORIES = "view_stories"
    SEND_DM = "send_dm"
    NAVIGATE_PROFILE = "navigate_profile"
    SCROLL_FEED = "scroll_feed"


@dataclass
class InstagramConfig:
    """Configuration for Instagram controller"""
    mcp_server_url: str = "http://localhost:8080"
    browser: str = "Chrome"  # or "Arc", "Safari", etc.
    instagram_url: str = "https://www.instagram.com"
    wait_time: float = 2.0  # Default wait time between actions
    max_retries: int = 3


class InstagramController:
    """Main Instagram controller class"""
    
    def __init__(self, config: InstagramConfig = None):
        self.config = config or InstagramConfig()
        self.session = requests.Session()
        self.current_elements = []
        self.is_logged_in = False
        
    def _make_mcp_request(self, function_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Make a request to the MCP server using executeToolFunction"""
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "executeToolFunction",
            "params": {
                "function": function_name,
                "arguments": arguments
            }
        }

        response = self.session.post(
            f"{self.config.mcp_server_url}/mcp",
            json=payload,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code != 200:
            raise Exception(f"MCP request failed: {response.status_code} - {response.text}")

        result = response.json()
        if "error" in result:
            raise Exception(f"MCP error: {result['error']}")

        return result.get("result", {})
    
    def _wait(self, seconds: float = None):
        """Wait for a specified time"""
        time.sleep(seconds or self.config.wait_time)
    
    def open_instagram(self) -> bool:
        """Open Instagram in the browser"""
        print("🌐 Opening Instagram...")

        try:
            result = self._make_mcp_request("openUrl", {
                "url": self.config.instagram_url,
                "browser": self.config.browser
            })

            self._wait(3)  # Wait for page to load
            self._refresh_elements()
            print("✅ Instagram opened successfully")
            return True

        except Exception as e:
            print(f"❌ Failed to open Instagram: {e}")
            return False
    
    def _refresh_elements(self) -> List[Dict]:
        """Refresh the list of interactive elements on the page"""
        try:
            result = self._make_mcp_request("listInteractableElements", {
                "app_name": self.config.browser
            })

            self.current_elements = result.get("elements", [])
            print(f"📋 Found {len(self.current_elements)} interactive elements")
            return self.current_elements

        except Exception as e:
            print(f"❌ Failed to refresh elements: {e}")
            return []
    
    def _find_element_by_text(self, text: str, partial: bool = True) -> Optional[Dict]:
        """Find an element by its text content"""
        for i, element in enumerate(self.current_elements):
            element_text = element.get("text", "").lower()
            search_text = text.lower()
            
            if partial and search_text in element_text:
                element["index"] = i
                return element
            elif not partial and search_text == element_text:
                element["index"] = i
                return element
        
        return None
    
    def _find_element_by_role(self, role: str) -> Optional[Dict]:
        """Find an element by its role"""
        for i, element in enumerate(self.current_elements):
            if element.get("role", "").lower() == role.lower():
                element["index"] = i
                return element
        return None
    
    def _click_element(self, element_index: int) -> bool:
        """Click an element by its index"""
        try:
            result = self._make_mcp_request("clickByIndex", {
                "app_name": self.config.browser,
                "element_index": element_index
            })

            self._wait()
            return True

        except Exception as e:
            print(f"❌ Failed to click element {element_index}: {e}")
            return False
    
    def _type_text(self, element_index: int, text: str) -> bool:
        """Type text into an element"""
        try:
            result = self._make_mcp_request("typeByIndex", {
                "app_name": self.config.browser,
                "element_index": element_index,
                "text": text
            })

            self._wait()
            return True

        except Exception as e:
            print(f"❌ Failed to type text: {e}")
            return False
    
    def login(self, username: str = None, password: str = None) -> bool:
        """Login to Instagram"""
        print("🔐 Attempting to login...")
        
        # Check if already logged in
        self._refresh_elements()
        if self._find_element_by_text("home", partial=True):
            print("✅ Already logged in")
            self.is_logged_in = True
            return True
        
        # Look for login elements
        username_field = self._find_element_by_text("username") or self._find_element_by_text("phone")
        password_field = self._find_element_by_text("password")
        login_button = self._find_element_by_text("log in") or self._find_element_by_text("sign in")
        
        if not all([username_field, password_field, login_button]):
            print("❌ Could not find login form elements")
            return False
        
        # Get credentials from environment if not provided
        if not username:
            username = os.getenv("INSTAGRAM_USERNAME")
        if not password:
            password = os.getenv("INSTAGRAM_PASSWORD")
            
        if not username or not password:
            print("❌ Username and password required (set INSTAGRAM_USERNAME and INSTAGRAM_PASSWORD env vars)")
            return False
        
        # Fill in credentials
        print("📝 Entering credentials...")
        if not self._type_text(username_field["index"], username):
            return False
            
        if not self._type_text(password_field["index"], password):
            return False
        
        # Click login
        if not self._click_element(login_button["index"]):
            return False
        
        # Wait for login to complete
        self._wait(5)
        self._refresh_elements()
        
        # Check if login was successful
        if self._find_element_by_text("home", partial=True):
            print("✅ Login successful")
            self.is_logged_in = True
            return True
        else:
            print("❌ Login failed")
            return False

    def search(self, query: str, search_type: str = "user") -> bool:
        """Search for users or hashtags"""
        print(f"🔍 Searching for {search_type}: {query}")

        self._refresh_elements()

        # Find search box
        search_box = (self._find_element_by_text("search") or
                     self._find_element_by_role("textbox") or
                     self._find_element_by_text("search and explore"))

        if not search_box:
            print("❌ Could not find search box")
            return False

        # Click search box and enter query
        if not self._click_element(search_box["index"]):
            return False

        search_query = f"#{query}" if search_type == "hashtag" else query
        if not self._type_text(search_box["index"], search_query):
            return False

        # Press Enter to search
        try:
            self._make_mcp_request("pressKeyByIndex", {
                "app_name": self.config.browser,
                "element_index": search_box["index"],
                "key_combo": "Return"
            })
        except Exception as e:
            print(f"❌ Failed to press Enter: {e}")
            return False

        self._wait(3)  # Wait for search results
        self._refresh_elements()
        print("✅ Search completed")
        return True

    def like_post(self, post_index: int = 0) -> bool:
        """Like a post (by default, the first visible post)"""
        print(f"❤️ Liking post {post_index}...")

        self._refresh_elements()

        # Look for like buttons (heart icons)
        like_buttons = []
        for i, element in enumerate(self.current_elements):
            text = element.get("text", "").lower()
            role = element.get("role", "").lower()

            if ("like" in text and "button" in role) or "heart" in text:
                like_buttons.append({"index": i, "element": element})

        if not like_buttons:
            print("❌ No like buttons found")
            return False

        if post_index >= len(like_buttons):
            print(f"❌ Post index {post_index} out of range (found {len(like_buttons)} posts)")
            return False

        # Click the like button
        target_button = like_buttons[post_index]
        if self._click_element(target_button["index"]):
            print("✅ Post liked successfully")
            return True
        else:
            print("❌ Failed to like post")
            return False

    def follow_user(self, username: str = None) -> bool:
        """Follow a user (if on their profile page)"""
        print(f"👥 Following user...")

        self._refresh_elements()

        # Look for follow button
        follow_button = (self._find_element_by_text("follow") or
                        self._find_element_by_text("follow back"))

        if not follow_button:
            print("❌ Follow button not found (are you on a user's profile?)")
            return False

        if self._click_element(follow_button["index"]):
            print("✅ User followed successfully")
            return True
        else:
            print("❌ Failed to follow user")
            return False

    def send_direct_message(self, message: str, username: str = None) -> bool:
        """Send a direct message"""
        print(f"💬 Sending DM: {message}")

        # Navigate to DMs first
        self._refresh_elements()

        # Look for message/DM button
        dm_button = (self._find_element_by_text("message") or
                    self._find_element_by_text("direct") or
                    self._find_element_by_text("send message"))

        if dm_button:
            if not self._click_element(dm_button["index"]):
                return False
            self._wait(2)
            self._refresh_elements()

        # Find message input field
        message_input = (self._find_element_by_text("message") or
                        self._find_element_by_role("textbox"))

        if not message_input:
            print("❌ Could not find message input field")
            return False

        # Type and send message
        if not self._type_text(message_input["index"], message):
            return False

        # Look for send button
        send_button = (self._find_element_by_text("send") or
                      self._find_element_by_text("submit"))

        if send_button:
            if self._click_element(send_button["index"]):
                print("✅ Message sent successfully")
                return True

        # Try pressing Enter if no send button found
        try:
            self._make_mcp_request("pressKeyByIndex", {
                "app_name": self.config.browser,
                "element_index": message_input["index"],
                "key_combo": "Return"
            })
            print("✅ Message sent successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
            return False

    def scroll_feed(self, direction: str = "down", amount: int = 3) -> bool:
        """Scroll the Instagram feed"""
        print(f"📜 Scrolling {direction}...")

        try:
            for _ in range(amount):
                self._make_mcp_request("inputControl", {
                    "action": {
                        "Scroll": {
                            "direction": direction,
                            "amount": 500
                        }
                    }
                })
                self._wait(1)

            self._refresh_elements()
            print("✅ Scrolling completed")
            return True

        except Exception as e:
            print(f"❌ Failed to scroll: {e}")
            return False

    def navigate_to_profile(self, username: str) -> bool:
        """Navigate to a specific user's profile"""
        print(f"👤 Navigating to @{username}'s profile...")

        # Use direct URL navigation
        profile_url = f"{self.config.instagram_url}/{username}"

        try:
            result = self._make_mcp_request("openUrl", {
                "url": profile_url,
                "browser": self.config.browser
            })

            self._wait(3)
            self._refresh_elements()
            print(f"✅ Navigated to @{username}'s profile")
            return True

        except Exception as e:
            print(f"❌ Failed to navigate to profile: {e}")
            return False

    def view_stories(self) -> bool:
        """View Instagram stories"""
        print("📖 Viewing stories...")

        self._refresh_elements()

        # Look for story elements (usually circular profile pictures at the top)
        story_elements = []
        for i, element in enumerate(self.current_elements):
            text = element.get("text", "").lower()
            role = element.get("role", "").lower()

            if ("story" in text or "stories" in text) and "button" in role:
                story_elements.append({"index": i, "element": element})

        if not story_elements:
            print("❌ No stories found")
            return False

        # Click the first story
        if self._click_element(story_elements[0]["index"]):
            print("✅ Opened story viewer")
            self._wait(3)
            return True
        else:
            print("❌ Failed to open stories")
            return False

    def comment_on_post(self, comment_text: str, post_index: int = 0) -> bool:
        """Comment on a post"""
        print(f"💬 Commenting on post {post_index}: {comment_text}")

        self._refresh_elements()

        # Look for comment buttons
        comment_buttons = []
        for i, element in enumerate(self.current_elements):
            text = element.get("text", "").lower()
            role = element.get("role", "").lower()

            if "comment" in text and "button" in role:
                comment_buttons.append({"index": i, "element": element})

        if not comment_buttons:
            print("❌ No comment buttons found")
            return False

        if post_index >= len(comment_buttons):
            print(f"❌ Post index {post_index} out of range")
            return False

        # Click comment button
        if not self._click_element(comment_buttons[post_index]["index"]):
            return False

        self._wait(2)
        self._refresh_elements()

        # Find comment input field
        comment_input = self._find_element_by_text("add a comment") or self._find_element_by_role("textbox")

        if not comment_input:
            print("❌ Could not find comment input field")
            return False

        # Type comment
        if not self._type_text(comment_input["index"], comment_text):
            return False

        # Submit comment (usually Enter key)
        try:
            self._make_mcp_request("pressKeyByIndex", {
                "app_name": self.config.browser,
                "element_index": comment_input["index"],
                "key_combo": "Return"
            })
            print("✅ Comment posted successfully")
            return True
        except Exception as e:
            print(f"❌ Failed to post comment: {e}")
            return False

    def get_current_page_info(self) -> Dict[str, Any]:
        """Get information about the current page"""
        self._refresh_elements()

        info = {
            "total_elements": len(self.current_elements),
            "page_type": "unknown",
            "interactive_elements": []
        }

        # Analyze elements to determine page type
        text_content = " ".join([elem.get("text", "") for elem in self.current_elements]).lower()

        if "home" in text_content and "explore" in text_content:
            info["page_type"] = "home_feed"
        elif "followers" in text_content and "following" in text_content:
            info["page_type"] = "profile"
        elif "search" in text_content and "recent" in text_content:
            info["page_type"] = "search_results"
        elif "direct" in text_content or "messages" in text_content:
            info["page_type"] = "direct_messages"

        # Collect interactive elements
        for i, element in enumerate(self.current_elements):
            if element.get("role") in ["button", "link", "textbox"]:
                info["interactive_elements"].append({
                    "index": i,
                    "text": element.get("text", ""),
                    "role": element.get("role", ""),
                    "type": element.get("type", "")
                })

        return info

    def print_current_elements(self, limit: int = 20):
        """Print current page elements for debugging"""
        self._refresh_elements()

        print(f"\n📋 Current page elements (showing first {limit}):")
        print("-" * 80)

        for i, element in enumerate(self.current_elements[:limit]):
            text = element.get("text", "")[:50]  # Truncate long text
            role = element.get("role", "")
            element_type = element.get("type", "")

            print(f"{i:3d}: {role:12s} | {element_type:12s} | {text}")

        if len(self.current_elements) > limit:
            print(f"... and {len(self.current_elements) - limit} more elements")

        print("-" * 80)


def main():
    """Main function to demonstrate Instagram controller"""
    print("🚀 Instagram Browser Controller")
    print("=" * 50)

    # Initialize controller
    config = InstagramConfig()
    controller = InstagramController(config)

    # Interactive menu
    while True:
        print("\n📱 Instagram Controller Menu:")
        print("1. Open Instagram")
        print("2. Login")
        print("3. Search user")
        print("4. Search hashtag")
        print("5. Like first post")
        print("6. Follow user (on profile page)")
        print("7. Send DM")
        print("8. Scroll feed")
        print("9. Navigate to profile")
        print("10. View stories")
        print("11. Comment on post")
        print("12. Show current page info")
        print("13. Show page elements (debug)")
        print("0. Exit")

        try:
            choice = input("\nEnter your choice (0-13): ").strip()

            if choice == "0":
                print("👋 Goodbye!")
                break
            elif choice == "1":
                controller.open_instagram()
            elif choice == "2":
                controller.login()
            elif choice == "3":
                username = input("Enter username to search: ").strip()
                if username:
                    controller.search(username, "user")
            elif choice == "4":
                hashtag = input("Enter hashtag to search (without #): ").strip()
                if hashtag:
                    controller.search(hashtag, "hashtag")
            elif choice == "5":
                controller.like_post()
            elif choice == "6":
                controller.follow_user()
            elif choice == "7":
                message = input("Enter message to send: ").strip()
                if message:
                    controller.send_direct_message(message)
            elif choice == "8":
                direction = input("Scroll direction (up/down) [down]: ").strip() or "down"
                controller.scroll_feed(direction)
            elif choice == "9":
                username = input("Enter username (without @): ").strip()
                if username:
                    controller.navigate_to_profile(username)
            elif choice == "10":
                controller.view_stories()
            elif choice == "11":
                comment = input("Enter comment text: ").strip()
                if comment:
                    controller.comment_on_post(comment)
            elif choice == "12":
                info = controller.get_current_page_info()
                print(f"\n📊 Page Info:")
                print(f"Type: {info['page_type']}")
                print(f"Total elements: {info['total_elements']}")
                print(f"Interactive elements: {len(info['interactive_elements'])}")
            elif choice == "13":
                limit = input("Number of elements to show [20]: ").strip()
                limit = int(limit) if limit.isdigit() else 20
                controller.print_current_elements(limit)
            else:
                print("❌ Invalid choice. Please try again.")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
