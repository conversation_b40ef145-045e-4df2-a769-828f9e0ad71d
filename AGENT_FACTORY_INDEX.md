# Agent Factory Central Notion Index

## [Agent Factory Central Notion](https://www.notion.so/1a682d3947a6807e92ace67245ac2ab6)
Central hub for R&D projects, agent foundations, prompt swarm safety, project specs, hiring notes, flows/pipelines, and architecture plans.

### Subpages & Key Sections

- [R&D .Specs Master](https://www.notion.so/1b882d3947a6808494dae6cebfa254f9)
  - **Purpose:** Master file for hierarchical storage of specs, decisions, and edit history. Contains project norms, formatting, and links to collateral.
  - **Norms and Formatting:** Emphasizes organization, nesting, and consistent verbiage. Avoids siloed files and encourages clarity.
  - **Subpages:**
    - [Agent Factory Readme → how to use this Notion](https://www.notion.so/1b882d3947a6809a8d7ce1f3f5b66163): Guidelines for keeping the Notion workspace organized and clean, with a focus on nesting and clarity.
    - [University-Grade Exam Question Generator Spec](https://www.notion.so/1d982d3947a680e1b520f3f0179eb181): Detailed system and user instructions for generating challenging university-level exam questions, including principles for effective domain composition and quality guidelines.
    - [Repo/Data/Idea Management](https://www.notion.so/1d882d3947a680ea8481d3df1b511132): Steps for managing repositories, on-demand data, and idea tracking.
    - [Client/Enterprise Feedback & History](https://www.notion.so/1d082d3947a680819da4f7e5bfd17dad): Client list, feedback provider history, and enterprise customer details.
    - [Adversarial Safety - Ben L](https://www.notion.so/1d082d3947a680d59d23d19179e06823): Adversarial safety notes and proposal links.
      - [Haize Proposal 1](https://www.notion.so/1d082d3947a680ee8cc8c780f597c99d): Linked proposal document.
    - [Recursive Solution & Vellum Export](https://www.notion.so/1d182d3947a68093b2fde8ae454e792e): Links to recursive solutions, Vellum exports, and diversity evaluation notes.
    - [Convo Planning & Demo](https://www.notion.so/1bc82d3947a68093a9b0c07a63c056f2): Planning notes, demo content, goals, and value statements for agentic teams.
    - [Model Breaker & Seed Generation](https://www.notion.so/1c782d3947a6805cbf27d20a7704a185): Tasks for model breaker, seed generation, and Anthropic model breaker internal share.
      - [ANTHROPIC MODEL BREAKER [Internal Share]](https://www.notion.so/1c982d3947a680738d90d34c5635f0fe): Anthropic model breaker details.
    - [Safety Flow & Chat Export](https://www.notion.so/1c782d3947a680e7a947efe9081eea91): Safety flow notes and chat export.
      - [Safety Flow 1 - chat export](https://www.notion.so/1c782d3947a680c7a92cc4a8dce367a8): Exported chat for safety flow.
    - [Spec Driven Development & Team Directory](https://www.notion.so/1bc82d3947a68090af88f861ff2c4d9f): Team directory, spec-driven development exploration, and partner notes.
      - [David Domac - Action Page - Autoseeder_v4 Model Breaker](https://www.notion.so/1bb82d3947a680e9b3a4e4f2d27da2de): Action page for model breaker.
    - [Agent Tools & Architecture](https://www.notion.so/1b982d3947a680dc9902d68f826bf5a4): Lists of agent tools, tasks, architecture, and core tools with standardized schemas.
      - [prompts → stimulating certain kinds of response wisdom](https://www.notion.so/1bf82d3947a680279847f06b6cba6853): Prompt design and wisdom.
    - [Spec Driven Development Example](https://www.notion.so/1bb82d3947a6801a8b24e6f3cf26f841): Example of specification-driven development, workflow engine, and documentation.
    - [Agent Factory Architecture](https://www.notion.so/1ba82d3947a6808eb308d56ed421caa4): High-level architecture diagrams, core services, data pipeline, and database schemas.
    - [Dropboxes & Prompts](https://www.notion.so/********************************): General dropbox for ideas and prompt design.
    - [MCP Inspector / Debugger](https://www.notion.so/1be82d3947a6808697d9ed8abc86d7a7): Inspector and debugger tools for MCP.
    - [MCP Agents](https://www.notion.so/1bb82d3947a680e8b956cc4fdd5afd6e): Agent definitions and lists.
    - [Naming Hierarchy](https://www.notion.so/1bb82d3947a68087a001fb30213bf39f): Notes on naming conventions and clarity.
    - [Async Data Pipelines](https://www.notion.so/1ba82d3947a680b5bf6ae2c4e2fa16b1): Notes on async pipelines, code samples, and tool routers.

- [Agent Factory and Agent Foundations - Pitch to Pi](https://www.notion.so/1b882d3947a6805c9e42cc41af256b53)
  - **Purpose:** Pitch document for agent factory and foundational concepts.

- [Shit Alex Said in Slack](https://www.notion.so/1b482d3947a680709759f67aba195036)
  - **Purpose:** Collection of notable Slack messages and insights from Alex.

- [The Name of the Wind — Naming Hierarchy](https://www.notion.so/1b482d3947a68051a955e62c9cc93392)
  - **Purpose:** Naming conventions and hierarchy for projects and agents.

- [Input/output schema for Prompt Swarm JSON Req/Res](https://www.notion.so/1a682d3947a680d7a1eef276307b8ac8)
  - **Purpose:** Schemas for prompt swarm input/output in JSON format.

- [QA interview / Dean chat](https://www.notion.so/1a982d3947a68027b793ff160db9cc0a)
  - **Purpose:** QA interview notes and chat with Dean.

- [Prompt Swarm Test Cases Feb](https://www.notion.so/1ac82d3947a6806c8d17ddee5ba78c84)
  - **Purpose:** Test cases for prompt swarm, February edition.

- [R&D Update Emails](https://www.notion.so/1b582d3947a680e1b9a5c20d7cf2a4eb)
  - **Purpose:** Archive of R&D update emails.

- [Random AF](https://www.notion.so/1bb82d3947a68085a3b3c3a5e0919b82)
  - **Purpose:** Miscellaneous notes and ideas.

- [Agent Constraints (Prompt Swarm)](https://www.notion.so/1d582d3947a6800aba41d781e79b7e9b)
  - **Purpose:** Constraints and requirements for prompt swarm agents.

---

**This index now includes all subpages and their subpages, nested with tiered bullets, and includes titles, URLs, and short descriptions.**

This index is a living document. For the latest, see the [Agent Factory Central Notion](https://www.notion.so/1a682d3947a6807e92ace67245ac2ab6) and its subpages. 