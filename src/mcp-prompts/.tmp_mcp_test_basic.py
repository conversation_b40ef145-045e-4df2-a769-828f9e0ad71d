#!/usr/bin/env python3
"""Basic MCP test script to understand the computer control API."""

import requests
import json
import time
import random


class MCPClient:
    """Simple MCP client for testing."""
    
    def __init__(self, base_url="http://127.0.0.1:8080/mcp"):
        self.base_url = base_url
        self.request_id = 0
    
    def _make_request(self, method, params=None):
        """Make an MCP JSON-RPC request."""
        self.request_id += 1
        payload = {
            "jsonrpc": "2.0",
            "id": self.request_id,
            "method": method,
            "params": params or {}
        }
        
        print(f"\n[Request] {method}")
        print(f"Params: {json.dumps(params, indent=2)}")
        
        response = requests.post(self.base_url, json=payload)
        result = response.json()
        
        if "error" in result:
            print(f"[Error] {result['error']}")
        else:
            print(f"[Response] Success")
            if "result" in result:
                print(json.dumps(result["result"], indent=2, default=str)[:500] + "...")
        
        return result
    
    def initialize(self):
        """Initialize MCP connection and get capabilities."""
        return self._make_request("initialize", {
            "capabilities": {
                "tools": {"execution": True},
                "resources": {}
            }
        })
    
    def call_tool(self, function_name, arguments):
        """Call a specific tool function."""
        return self._make_request("executeToolFunction", {
            "function": function_name,
            "arguments": arguments
        })


def test_basic_operations():
    """Test basic MCP operations."""
    client = MCPClient()
    
    print("=== MCP Basic Test Suite ===\n")
    
    # 1. Initialize
    print("1. Initializing MCP connection...")
    init_result = client.initialize()
    
    # Extract available tools
    if "result" in init_result:
        tools = init_result["result"]["capabilities"]["tools"]["functions"]
        print(f"\nAvailable tools: {[t['name'] for t in tools]}")
    
    # 2. List interactable elements from current focused app
    print("\n2. Listing interactable elements from focused app...")
    list_result = client.call_tool("listInteractableElements", {
        "interactable_only": True,
        "max_elements": 20
    })
    
    # 3. Open a simple app
    print("\n3. Opening TextEdit...")
    open_result = client.call_tool("openApplication", {
        "app_name": "TextEdit"
    })
    
    time.sleep(2)  # Wait for app to open
    
    # 4. List elements from TextEdit
    print("\n4. Listing elements from TextEdit...")
    textedit_elements = client.call_tool("listInteractableElements", {
        "app_name": "TextEdit",
        "interactable_only": True,
        "max_elements": 30
    })
    
    # 5. Try to type in a text field if found
    if "result" in textedit_elements and "elements" in textedit_elements["result"]:
        elements = textedit_elements["result"]["elements"]
        text_fields = [i for i, el in enumerate(elements) 
                      if el.get("role") in ["AXTextArea", "AXTextField"]]
        
        if text_fields:
            print(f"\n5. Found text field at index {text_fields[0]}, typing...")
            type_result = client.call_tool("typeByIndex", {
                "element_index": text_fields[0],
                "text": "Hello from MCP computer control test!"
            })
    
    # 6. Press Escape to close
    print("\n6. Pressing Escape key...")
    escape_result = client.call_tool("inputControl", {
        "action": {
            "type": "KeyPress",
            "data": "Escape"
        }
    })
    
    print("\n=== Test Complete ===")


def test_browser_control():
    """Test browser control operations."""
    client = MCPClient()
    
    print("\n=== Browser Control Test ===\n")
    
    # Initialize
    client.initialize()
    
    # Open browser with URL
    print("1. Opening browser with example.com...")
    browser_result = client.call_tool("openUrl", {
        "url": "https://example.com",
        "browser": "Safari"  # or "Arc", "Chrome"
    })
    
    time.sleep(3)  # Wait for page to load
    
    # List elements on the page
    print("\n2. Listing page elements...")
    page_elements = client.call_tool("listInteractableElements", {
        "app_name": "Safari",
        "interactable_only": True,
        "max_elements": 20
    })
    
    # Try to find and click a link
    if "result" in page_elements and "elements" in page_elements["result"]:
        elements = page_elements["result"]["elements"]
        links = [i for i, el in enumerate(elements) 
                if el.get("role") == "AXLink"]
        
        if links:
            print(f"\n3. Found link at index {links[0]}, clicking...")
            click_result = client.call_tool("clickByIndex", {
                "element_index": links[0]
            })
    
    print("\n=== Browser Test Complete ===")


if __name__ == "__main__":
    # Test basic operations
    test_basic_operations()
    
    # Wait a bit before browser test
    time.sleep(2)
    
    # Test browser control
    test_browser_control()