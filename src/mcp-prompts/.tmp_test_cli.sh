#!/bin/bash
# Test script to understand MCP CLI interface

cd /Users/<USER>/Documents/Code2/MCP-server-client-computer-use-ai-sdk/src/mcp-client-cli-interface

# Set API key if available
export ANTHROPIC_API_KEY="${ANTHROPIC_API_KEY:-sk-ant-test}"

# Test 1: List all applications
echo "=== Test 1: List Applications ==="
echo "list all running applications" | npx tsx main.ts --non-interactive

# Test 2: Open and interact with TextEdit
echo -e "\n=== Test 2: Open TextEdit ==="
echo "open TextEdit application and type 'Hello from MCP test'" | npx tsx main.ts --non-interactive

# Test 3: Browser interaction
echo -e "\n=== Test 3: Browser Test ==="
echo "open https://example.com in Safari browser" | npx tsx main.ts --non-interactive