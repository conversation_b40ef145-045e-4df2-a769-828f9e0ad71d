import axios from 'axios';

// Direct MCP test - understanding the protocol

async function testMCP() {
  const mcpUrl = 'http://127.0.0.1:8080/mcp';
  
  console.log('=== MCP Protocol Test ===\n');
  
  // Helper to make requests
  const makeRequest = async (method, params) => {
    const payload = {
      jsonrpc: '2.0',
      id: Math.floor(Math.random() * 10000),
      method,
      params
    };
    
    console.log(`\n[Request] ${method}`);
    console.log('Params:', JSON.stringify(params, null, 2));
    
    try {
      const response = await axios.post(mcpUrl, payload);
      console.log('[Response] Success');
      if (response.data.result) {
        console.log(JSON.stringify(response.data.result, null, 2).substring(0, 500) + '...');
      }
      return response.data;
    } catch (error) {
      console.error('[Error]', error.message);
      return null;
    }
  };
  
  // 1. Initialize and get capabilities
  console.log('1. Getting capabilities...');
  const initResult = await makeRequest('initialize', {
    capabilities: {
      tools: { execution: true },
      resources: {}
    }
  });
  
  if (initResult?.result?.capabilities?.tools?.functions) {
    const tools = initResult.result.capabilities.tools.functions;
    console.log('\nAvailable tools:');
    tools.forEach(t => {
      console.log(`  - ${t.name}: ${t.description || 'No description'}`);
    });
  }
  
  // 2. Test listing elements (no app specified - uses focused app)
  console.log('\n2. Listing elements from focused app...');
  const listResult = await makeRequest('executeToolFunction', {
    function: 'listInteractableElements',
    arguments: {
      interactable_only: true,
      max_elements: 10
    }
  });
  
  // 3. Open a simple app
  console.log('\n3. Opening Calculator...');
  const openResult = await makeRequest('executeToolFunction', {
    function: 'openApplication',
    arguments: {
      app_name: 'Calculator'
    }
  });
  
  // Wait for app
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 4. List Calculator elements
  console.log('\n4. Listing Calculator elements...');
  const calcElements = await makeRequest('executeToolFunction', {
    function: 'listInteractableElements',
    arguments: {
      app_name: 'Calculator',
      interactable_only: true,
      max_elements: 30
    }
  });
  
  // 5. Try clicking a button (if found)
  if (calcElements?.result?.elements?.length > 0) {
    const buttons = calcElements.result.elements.filter((el, idx) => {
      return el.role === 'AXButton' && el.title && /[0-9]/.test(el.title);
    });
    
    if (buttons.length > 0) {
      const buttonIndex = calcElements.result.elements.findIndex(el => 
        el === buttons[0]
      );
      
      console.log(`\n5. Clicking button "${buttons[0].title}" at index ${buttonIndex}...`);
      await makeRequest('executeToolFunction', {
        function: 'clickByIndex',
        arguments: {
          element_index: buttonIndex
        }
      });
    }
  }
  
  // 6. Close with Cmd+Q
  console.log('\n6. Closing Calculator...');
  await makeRequest('executeToolFunction', {
    function: 'inputControl',
    arguments: {
      action: {
        type: 'KeyPress',
        data: 'Cmd+Q'
      }
    }
  });
  
  console.log('\n=== Test Complete ===');
}

testMCP().catch(console.error);