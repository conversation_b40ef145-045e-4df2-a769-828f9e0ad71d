import axios from 'axios';

// Helper function to create an MCP request
function createMCPRequest(method, params) {
  return {
    jsonrpc: '2.0',
    id: Math.floor(Math.random() * 10000),
    method,
    params
  };
}

async function testMCP() {
  const mcpUrl = 'http://127.0.0.1:8080/mcp';
  console.log('=== Simple MCP Test ===\n');

  try {
    // 1. Initialize to get capabilities
    console.log('1. Initialize and get capabilities...');
    const initResponse = await axios.post(mcpUrl, createMCPRequest('initialize', {
      capabilities: {
        tools: { execution: true },
        resources: {}
      }
    }));
    
    const tools = initResponse.data.result.capabilities.tools.functions;
    console.log('\nAvailable tools:');
    tools.forEach(t => {
      console.log(`  - ${t.name}`);
    });

    // 2. Simple test: Open TextEdit and type something
    console.log('\n2. Opening TextEdit...');
    const openAppResponse = await axios.post(mcpUrl, createMCPRequest('executeToolFunction', {
      function: 'openApplication',
      arguments: {
        app_name: 'TextEdit'
      }
    }));
    console.log('Result:', openAppResponse.data.result?.application || openAppResponse.data.result);

    // Wait for app to open
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 3. List elements from TextEdit
    console.log('\n3. Finding text areas in TextEdit...');
    const listElementsResponse = await axios.post(mcpUrl, createMCPRequest('executeToolFunction', {
      function: 'listInteractableElements',
      arguments: {
        app_name: 'TextEdit',
        interactable_only: true,
        max_elements: 20
      }
    }));
    
    const elements = listElementsResponse.data.result.elements;
    console.log(`Found ${elements.length} elements`);
    
    // Find text area
    const textAreaIndex = elements.findIndex(el => 
      el.role === 'AXTextArea' || el.role === 'AXTextField'
    );
    
    if (textAreaIndex >= 0) {
      console.log(`\n4. Found text area at index ${textAreaIndex}, typing...`);
      
      // Type some text
      const typeResponse = await axios.post(mcpUrl, createMCPRequest('executeToolFunction', {
        function: 'typeByIndex',
        arguments: {
          element_index: textAreaIndex,
          text: 'Hello from MCP Computer Control!\n\nThis is an automated test.'
        }
      }));
      console.log('Typing result:', typeResponse.data.result.success);
      
      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Press some keys
      console.log('\n5. Pressing Enter key...');
      const keyResponse = await axios.post(mcpUrl, createMCPRequest('executeToolFunction', {
        function: 'pressKeyByIndex',
        arguments: {
          element_index: textAreaIndex,
          key_combo: 'Enter'
        }
      }));
      console.log('Key press result:', keyResponse.data.result.success);
    }

    // 6. Close TextEdit
    console.log('\n6. Closing TextEdit (Cmd+Q)...');
    const closeResponse = await axios.post(mcpUrl, createMCPRequest('executeToolFunction', {
      function: 'inputControl',
      arguments: {
        action: {
          type: 'KeyPress',
          data: 'Cmd+Q'
        }
      }
    }));
    console.log('Close result:', closeResponse.data.result);
    
    // If save dialog appears, press Don't Save
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log('\n7. Pressing Cmd+D (Don\'t Save)...');
    const dontSaveResponse = await axios.post(mcpUrl, createMCPRequest('executeToolFunction', {
      function: 'inputControl',
      arguments: {
        action: {
          type: 'KeyPress',
          data: 'Cmd+D'
        }
      }
    }));

    console.log('\n=== Test Complete ===');
  } catch (error) {
    console.error('Error during testing:', error.response?.data || error.message);
  }
}

testMCP();