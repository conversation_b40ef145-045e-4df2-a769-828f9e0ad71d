# Email Client

A Python email automation client using built-in libraries (no external dependencies except Python standard library).

## Features

- **Send & Receive Emails** - Full SMTP/IMAP support
- **Multiple Providers** - Works with Gmail, Outlook, Yahoo, and any SMTP/IMAP server
- **Templates** - Pre-built templates for common email types
- **Automation** - Auto-replies, filters, bulk sending
- **Monitoring** - Background email monitoring with actions

## Setup

1. Set environment variables in `.env`:
```bash
EMAIL_ADDRESS=<EMAIL>
EMAIL_PASSWORD=your_app_password  # Use app-specific password for Gmail

# Optional custom server settings
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_IMAP_HOST=imap.gmail.com
EMAIL_IMAP_PORT=993
```

2. For Gmail users:
   - Enable 2-factor authentication
   - Generate an app-specific password
   - Use the app password instead of your regular password

## Quick Start

```python
from src.clients.email import EmailClient, EmailMessage

# Send email
with <PERSON>ail<PERSON>lient() as client:
    message = EmailMessage(
        to=["<EMAIL>"],
        subject="Hello",
        body="This is a test email"
    )
    client.send_email(message)

# Receive emails
with EmailClient() as client:
    emails = client.fetch_recent_emails(count=10)
    for email in emails:
        print(f"{email.from_addr}: {email.subject}")
```

## Templates

Built-in templates include:
- `welcome` - New user onboarding
- `status_update` - Project status updates
- `task_reminder` - Task reminders
- `meeting_invite` - Meeting invitations
- `error_notification` - System alerts

```python
from src.clients.email import EmailClient, TemplateManager

templates = TemplateManager()
rendered = templates.render_template(
    "welcome",
    name="John Doe",
    company_name="Acme Corp"
)
```

## Automation

```python
from src.clients.email import EmailAutomation, AutoReplyRule

automation = EmailAutomation(client)

# Auto-reply rule
automation.add_auto_reply_rule(AutoReplyRule(
    name="out_of_office",
    condition=lambda email: True,
    template_name="status_update",
    delay_seconds=5
))

# Process emails
automation.process_auto_replies()
```

## Examples

See `example_usage.py` for complete examples of all features.