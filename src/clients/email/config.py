"""Email configuration module."""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class EmailConfig:
    """Email server configuration."""
    
    # SMTP Settings
    smtp_host: str = os.getenv("EMAIL_SMTP_HOST", "smtp.gmail.com")
    smtp_port: int = int(os.getenv("EMAIL_SMTP_PORT", "587"))
    smtp_use_tls: bool = os.getenv("EMAIL_SMTP_USE_TLS", "true").lower() == "true"
    
    # IMAP Settings
    imap_host: str = os.getenv("EMAIL_IMAP_HOST", "imap.gmail.com")
    imap_port: int = int(os.getenv("EMAIL_IMAP_PORT", "993"))
    imap_use_ssl: bool = os.getenv("EMAIL_IMAP_USE_SSL", "true").lower() == "true"
    
    # Authentication
    email_address: str = os.getenv("EMAIL_ADDRESS", "")
    email_password: str = os.getenv("EMAIL_PASSWORD", "")
    
    # Optional OAuth2 (for Gmail and others)
    oauth2_token: Optional[str] = os.getenv("EMAIL_OAUTH2_TOKEN")
    
    # Defaults
    default_from_name: str = os.getenv("EMAIL_FROM_NAME", "Email Automation")
    
    def validate(self) -> None:
        """Validate required configuration."""
        if not self.email_address:
            raise ValueError("EMAIL_ADDRESS environment variable is required")
        if not self.email_password and not self.oauth2_token:
            raise ValueError("Either EMAIL_PASSWORD or EMAIL_OAUTH2_TOKEN is required")