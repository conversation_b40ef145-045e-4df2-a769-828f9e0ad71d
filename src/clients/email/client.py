"""Email client implementation using Python built-in libraries."""

import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import ssl
from datetime import datetime

from .config import EmailConfig


@dataclass
class EmailMessage:
    """Represents an email message."""
    to: List[str]
    subject: str
    body: str
    html_body: Optional[str] = None
    cc: Optional[List[str]] = None
    bcc: Optional[List[str]] = None
    attachments: Optional[List[Path]] = None
    headers: Optional[Dict[str, str]] = None


@dataclass
class ReceivedEmail:
    """Represents a received email."""
    uid: str
    from_addr: str
    to_addr: List[str]
    subject: str
    date: datetime
    body: str
    html_body: Optional[str] = None
    attachments: List[Dict[str, Any]] = None


class EmailClient:
    """Email client for sending and receiving emails."""
    
    def __init__(self, config: Optional[EmailConfig] = None):
        """Initialize email client with configuration."""
        self.config = config or EmailConfig()
        self.config.validate()
        self._smtp_connection = None
        self._imap_connection = None
    
    # ----- SMTP Methods -----
    
    def connect_smtp(self) -> None:
        """Connect to SMTP server."""
        if self.config.smtp_use_tls:
            self._smtp_connection = smtplib.SMTP(self.config.smtp_host, self.config.smtp_port)
            self._smtp_connection.starttls()
        else:
            self._smtp_connection = smtplib.SMTP_SSL(self.config.smtp_host, self.config.smtp_port)
        
        # Authenticate
        if self.config.oauth2_token:
            # OAuth2 authentication (for services that support it)
            auth_string = f"user={self.config.email_address}\x01auth=Bearer {self.config.oauth2_token}\x01\x01"
            self._smtp_connection.docmd("AUTH", "XOAUTH2 " + auth_string)
        else:
            self._smtp_connection.login(self.config.email_address, self.config.email_password)
    
    def disconnect_smtp(self) -> None:
        """Disconnect from SMTP server."""
        if self._smtp_connection:
            self._smtp_connection.quit()
            self._smtp_connection = None
    
    def send_email(self, message: EmailMessage) -> bool:
        """Send an email message."""
        try:
            # Create message
            if message.html_body:
                msg = MIMEMultipart('alternative')
                msg.attach(MIMEText(message.body, 'plain'))
                msg.attach(MIMEText(message.html_body, 'html'))
            else:
                msg = MIMEMultipart()
                msg.attach(MIMEText(message.body, 'plain'))
            
            # Set headers
            msg['From'] = f"{self.config.default_from_name} <{self.config.email_address}>"
            msg['To'] = ', '.join(message.to)
            msg['Subject'] = message.subject
            
            if message.cc:
                msg['Cc'] = ', '.join(message.cc)
            
            # Add custom headers
            if message.headers:
                for key, value in message.headers.items():
                    msg[key] = value
            
            # Add attachments
            if message.attachments:
                for attachment_path in message.attachments:
                    self._attach_file(msg, attachment_path)
            
            # Prepare recipients
            recipients = message.to
            if message.cc:
                recipients.extend(message.cc)
            if message.bcc:
                recipients.extend(message.bcc)
            
            # Connect if not connected
            if not self._smtp_connection:
                self.connect_smtp()
            
            # Send email
            self._smtp_connection.send_message(msg, to_addrs=recipients)
            return True
            
        except Exception as e:
            print(f"Error sending email: {e}")
            return False
    
    def _attach_file(self, msg: MIMEMultipart, file_path: Path) -> None:
        """Attach a file to the email message."""
        with open(file_path, 'rb') as f:
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(f.read())
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename={file_path.name}'
            )
            msg.attach(part)
    
    # ----- IMAP Methods -----
    
    def connect_imap(self) -> None:
        """Connect to IMAP server."""
        if self.config.imap_use_ssl:
            self._imap_connection = imaplib.IMAP4_SSL(self.config.imap_host, self.config.imap_port)
        else:
            self._imap_connection = imaplib.IMAP4(self.config.imap_host, self.config.imap_port)
        
        # Authenticate
        if self.config.oauth2_token:
            # OAuth2 authentication
            auth_string = f"user={self.config.email_address}\x01auth=Bearer {self.config.oauth2_token}\x01\x01"
            self._imap_connection.authenticate('XOAUTH2', lambda x: auth_string)
        else:
            self._imap_connection.login(self.config.email_address, self.config.email_password)
    
    def disconnect_imap(self) -> None:
        """Disconnect from IMAP server."""
        if self._imap_connection:
            try:
                self._imap_connection.close()
                self._imap_connection.logout()
            except:
                pass
            self._imap_connection = None
    
    def list_folders(self) -> List[str]:
        """List all email folders."""
        if not self._imap_connection:
            self.connect_imap()
        
        status, folders = self._imap_connection.list()
        if status == 'OK':
            return [folder.decode().split(' "/" ')[-1].strip('"') for folder in folders]
        return []
    
    def select_folder(self, folder: str = 'INBOX') -> Tuple[str, int]:
        """Select a folder and return status and message count."""
        if not self._imap_connection:
            self.connect_imap()
        
        status, data = self._imap_connection.select(folder)
        if status == 'OK':
            return status, int(data[0])
        return status, 0
    
    def search_emails(self, criteria: str = 'ALL', folder: str = 'INBOX') -> List[str]:
        """Search emails by criteria (e.g., 'UNSEEN', 'FROM "<EMAIL>"')."""
        self.select_folder(folder)
        status, data = self._imap_connection.search(None, criteria)
        
        if status == 'OK':
            return data[0].split()
        return []
    
    def fetch_email(self, uid: str) -> Optional[ReceivedEmail]:
        """Fetch a single email by UID."""
        try:
            status, data = self._imap_connection.fetch(uid, '(RFC822)')
            if status == 'OK':
                raw_email = data[0][1]
                msg = email.message_from_bytes(raw_email)
                return self._parse_email(uid, msg)
        except Exception as e:
            print(f"Error fetching email {uid}: {e}")
        return None
    
    def fetch_recent_emails(self, count: int = 10, folder: str = 'INBOX') -> List[ReceivedEmail]:
        """Fetch recent emails from a folder."""
        emails = []
        self.select_folder(folder)
        
        # Search for all emails
        email_ids = self.search_emails('ALL', folder)
        
        # Get the most recent emails
        for uid in email_ids[-count:]:
            email_msg = self.fetch_email(uid)
            if email_msg:
                emails.append(email_msg)
        
        return emails
    
    def _parse_email(self, uid: str, msg: email.message.Message) -> ReceivedEmail:
        """Parse an email message into ReceivedEmail object."""
        # Extract basic info
        from_addr = msg.get('From', '')
        to_addrs = msg.get('To', '').split(',')
        subject = msg.get('Subject', '')
        date_str = msg.get('Date', '')
        
        # Parse date
        try:
            date_tuple = email.utils.parsedate_to_datetime(date_str)
            date = date_tuple if date_tuple else datetime.now()
        except:
            date = datetime.now()
        
        # Extract body
        body = ""
        html_body = None
        attachments = []
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition", ""))
                
                # Get body
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    body = part.get_payload(decode=True).decode()
                elif content_type == "text/html" and "attachment" not in content_disposition:
                    html_body = part.get_payload(decode=True).decode()
                
                # Get attachments
                elif "attachment" in content_disposition:
                    filename = part.get_filename()
                    if filename:
                        attachments.append({
                            'filename': filename,
                            'content_type': content_type,
                            'size': len(part.get_payload(decode=True))
                        })
        else:
            body = msg.get_payload(decode=True).decode()
        
        return ReceivedEmail(
            uid=uid.decode() if isinstance(uid, bytes) else uid,
            from_addr=from_addr,
            to_addr=[addr.strip() for addr in to_addrs],
            subject=subject,
            date=date,
            body=body,
            html_body=html_body,
            attachments=attachments
        )
    
    def mark_as_read(self, uid: str) -> bool:
        """Mark an email as read."""
        try:
            self._imap_connection.store(uid, '+FLAGS', '\\Seen')
            return True
        except:
            return False
    
    def delete_email(self, uid: str) -> bool:
        """Mark an email for deletion."""
        try:
            self._imap_connection.store(uid, '+FLAGS', '\\Deleted')
            self._imap_connection.expunge()
            return True
        except:
            return False
    
    # ----- Context Manager Support -----
    
    def __enter__(self):
        """Enter context manager."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.disconnect_smtp()
        self.disconnect_imap()