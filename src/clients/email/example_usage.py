"""Example usage of the email client."""

import os
from pathlib import Path
from datetime import datetime

from .client import EmailClient, EmailMessage
from .config import EmailConfig
from .automation import EmailAutomation, AutoReplyRule
from .templates import TemplateManager


def example_basic_usage():
    """Basic email sending and receiving example."""
    # Create client with custom config
    config = EmailConfig(
        smtp_host="smtp.gmail.com",
        smtp_port=587,
        imap_host="imap.gmail.com",
        email_address=os.getenv("EMAIL_ADDRESS"),
        email_password=os.getenv("EMAIL_PASSWORD")
    )
    
    with EmailClient(config) as client:
        # Send a simple email
        message = EmailMessage(
            to=["<EMAIL>"],
            subject="Test Email",
            body="This is a test email sent from Python!"
        )
        
        if client.send_email(message):
            print("Email sent successfully!")
        
        # Fetch recent emails
        recent_emails = client.fetch_recent_emails(count=5)
        for email in recent_emails:
            print(f"From: {email.from_addr}")
            print(f"Subject: {email.subject}")
            print(f"Date: {email.date}")
            print("-" * 40)


def example_template_usage():
    """Example of using email templates."""
    client = EmailClient()
    template_manager = TemplateManager()
    
    # Use welcome template
    rendered = template_manager.render_template(
        "welcome",
        name="John Doe",
        company_name="Acme Corp"
    )
    
    message = EmailMessage(
        to=["<EMAIL>"],
        subject=rendered['subject'],
        body=rendered['body'],
        html_body=rendered.get('html_body')
    )
    
    client.send_email(message)
    
    # Create custom template
    from .templates import EmailTemplate
    
    custom_template = EmailTemplate(
        name="invoice",
        subject_template="Invoice #$invoice_number - $company_name",
        body_template="""Dear $customer_name,

Please find attached invoice #$invoice_number for $amount.

Payment is due by $due_date.

Thank you for your business!

$company_name""",
        default_vars={"company_name": "My Company"}
    )
    
    template_manager.add_template(custom_template)
    
    # Use custom template
    rendered = template_manager.render_template(
        "invoice",
        customer_name="Jane Smith",
        invoice_number="INV-001",
        amount="$1,500.00",
        due_date="2024-02-01"
    )
    
    print("Rendered invoice email:")
    print(f"Subject: {rendered['subject']}")
    print(f"Body: {rendered['body']}")


def example_automation():
    """Example of email automation features."""
    client = EmailClient()
    automation = EmailAutomation(client)
    
    # Add auto-reply rule for out-of-office
    automation.add_auto_reply_rule(AutoReplyRule(
        name="out_of_office",
        condition=lambda email: True,  # Reply to all emails
        template_name="status_update",
        template_vars={
            "name": "there",
            "project_name": "Email Response",
            "status": "Out of Office",
            "progress": "0",
            "update_date": datetime.now().strftime("%Y-%m-%d"),
            "summary": "I am currently out of office and will respond to your email upon my return.",
            "next_steps": "I'll get back to you as soon as possible.",
            "sender_name": "John Doe"
        },
        delay_seconds=5  # Wait 5 seconds before replying
    ))
    
    # Add filter to archive old emails
    from .automation import EmailFilter
    
    automation.add_filter(EmailFilter(
        name="archive_old_newsletters",
        condition=lambda email: (
            "newsletter" in email.subject.lower() and 
            (datetime.now() - email.date).days > 7
        ),
        action=lambda email, client: print(f"Would archive: {email.subject}")
    ))
    
    # Process emails
    replied_uids = automation.process_auto_replies()
    print(f"Auto-replied to {len(replied_uids)} emails")
    
    filter_results = automation.apply_filters()
    print(f"Filter results: {filter_results}")
    
    # Bulk email example
    recipients = [
        {"email": "<EMAIL>", "name": "User One"},
        {"email": "<EMAIL>", "name": "User Two"},
        {"email": "<EMAIL>", "name": "User Three"},
    ]
    
    results = automation.send_bulk_emails(
        template_name="welcome",
        recipients=recipients,
        batch_size=2,
        delay_between_batches=5
    )
    print(f"Bulk email results: {results}")


def example_monitoring():
    """Example of email monitoring."""
    client = EmailClient()
    automation = EmailAutomation(client)
    
    # Add auto-reply for specific senders
    automation.add_auto_reply_rule(AutoReplyRule(
        name="vip_reply",
        condition=automation.create_sender_filter("<EMAIL>"),
        template_name="task_reminder",
        template_vars={
            "name": "VIP",
            "task_name": "Your request",
            "due_date": "ASAP",
            "description": "I've received your email and will process it immediately.",
            "sender_name": "Support Team"
        }
    ))
    
    # Start monitoring (runs in background)
    automation.start_monitoring(check_interval=60)  # Check every minute
    
    print("Email monitoring started. Press Ctrl+C to stop.")
    try:
        # Keep running
        import time
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        automation.stop_monitoring()
        print("\nMonitoring stopped.")


if __name__ == "__main__":
    # Ensure environment variables are set
    if not os.getenv("EMAIL_ADDRESS") or not os.getenv("EMAIL_PASSWORD"):
        print("Please set EMAIL_ADDRESS and EMAIL_PASSWORD environment variables")
        exit(1)
    
    print("Email Client Examples")
    print("=" * 50)
    
    print("\n1. Basic Usage Example")
    example_basic_usage()
    
    print("\n2. Template Usage Example")
    example_template_usage()
    
    print("\n3. Automation Example")
    example_automation()
    
    # Uncomment to test monitoring
    # print("\n4. Monitoring Example")
    # example_monitoring()