"""Email automation features for common workflows."""

from typing import List, Dict, Optional, Callable, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import time
import threading
from pathlib import Path

from .client import EmailClient, EmailMessage, ReceivedEmail
from .templates import Template<PERSON>anager


@dataclass
class AutoReplyRule:
    """Rule for automatic email replies."""
    name: str
    condition: Callable[[ReceivedEmail], bool]
    template_name: str
    template_vars: Optional[Dict[str, Any]] = None
    delay_seconds: int = 0
    
    def matches(self, email: ReceivedEmail) -> bool:
        """Check if email matches this rule."""
        return self.condition(email)


@dataclass
class EmailFilter:
    """Filter for processing incoming emails."""
    name: str
    condition: Callable[[ReceivedEmail], bool]
    action: Callable[[ReceivedEmail, EmailClient], None]
    
    def apply(self, email: ReceivedEmail, client: EmailClient) -> None:
        """Apply filter action if condition matches."""
        if self.condition(email):
            self.action(email, client)


class EmailAutomation:
    """Email automation engine."""
    
    def __init__(self, client: EmailClient):
        self.client = client
        self.template_manager = TemplateManager()
        self.auto_reply_rules: List[AutoReplyRule] = []
        self.filters: List[EmailFilter] = []
        self._monitoring = False
        self._monitor_thread = None
        
        # Setup default filters
        self._setup_default_filters()
    
    def _setup_default_filters(self):
        """Setup commonly used email filters."""
        # Archive old emails
        self.add_filter(EmailFilter(
            name="archive_old",
            condition=lambda email: (datetime.now() - email.date).days > 30,
            action=lambda email, client: self._move_to_folder(email, "Archive", client)
        ))
    
    # ----- Auto Reply System -----
    
    def add_auto_reply_rule(self, rule: AutoReplyRule) -> None:
        """Add an auto-reply rule."""
        self.auto_reply_rules.append(rule)
    
    def process_auto_replies(self, folder: str = 'INBOX') -> List[str]:
        """Process emails and send auto-replies based on rules."""
        replied_to = []
        
        # Fetch recent emails
        emails = self.client.fetch_recent_emails(count=20, folder=folder)
        
        for email in emails:
            for rule in self.auto_reply_rules:
                if rule.matches(email):
                    if self._send_auto_reply(email, rule):
                        replied_to.append(email.uid)
                        self.client.mark_as_read(email.uid)
                    break  # Only apply first matching rule
        
        return replied_to
    
    def _send_auto_reply(self, original_email: ReceivedEmail, rule: AutoReplyRule) -> bool:
        """Send an auto-reply based on rule."""
        # Wait if delay specified
        if rule.delay_seconds > 0:
            time.sleep(rule.delay_seconds)
        
        # Prepare template variables
        template_vars = {
            'original_subject': original_email.subject,
            'original_sender': original_email.from_addr,
            'original_date': original_email.date.strftime('%Y-%m-%d %H:%M'),
            **(rule.template_vars or {})
        }
        
        # Render template
        rendered = self.template_manager.render_template(rule.template_name, **template_vars)
        if not rendered:
            return False
        
        # Create reply message
        reply = EmailMessage(
            to=[original_email.from_addr],
            subject=f"Re: {original_email.subject}",
            body=rendered['body'],
            html_body=rendered.get('html_body'),
            headers={'In-Reply-To': f'<{original_email.uid}@email>'}
        )
        
        return self.client.send_email(reply)
    
    # ----- Email Filters -----
    
    def add_filter(self, filter: EmailFilter) -> None:
        """Add an email filter."""
        self.filters.append(filter)
    
    def apply_filters(self, folder: str = 'INBOX') -> Dict[str, int]:
        """Apply all filters to emails in folder."""
        results = {filter.name: 0 for filter in self.filters}
        
        emails = self.client.fetch_recent_emails(count=50, folder=folder)
        
        for email in emails:
            for filter in self.filters:
                try:
                    if filter.condition(email):
                        filter.apply(email, self.client)
                        results[filter.name] += 1
                except Exception as e:
                    print(f"Error applying filter {filter.name}: {e}")
        
        return results
    
    def _move_to_folder(self, email: ReceivedEmail, folder: str, client: EmailClient) -> None:
        """Move email to specified folder."""
        # This is a simplified version - actual implementation depends on IMAP server
        client._imap_connection.copy(email.uid, folder)
        client.delete_email(email.uid)
    
    # ----- Bulk Operations -----
    
    def send_bulk_emails(self, template_name: str, recipients: List[Dict[str, Any]], 
                        batch_size: int = 50, delay_between_batches: int = 60) -> Dict[str, int]:
        """Send bulk emails using a template."""
        results = {'sent': 0, 'failed': 0}
        
        for i in range(0, len(recipients), batch_size):
            batch = recipients[i:i + batch_size]
            
            for recipient in batch:
                try:
                    # Render template with recipient data
                    rendered = self.template_manager.render_template(template_name, **recipient)
                    if not rendered:
                        results['failed'] += 1
                        continue
                    
                    # Create and send email
                    message = EmailMessage(
                        to=[recipient.get('email', recipient.get('to'))],
                        subject=rendered['subject'],
                        body=rendered['body'],
                        html_body=rendered.get('html_body')
                    )
                    
                    if self.client.send_email(message):
                        results['sent'] += 1
                    else:
                        results['failed'] += 1
                    
                except Exception as e:
                    print(f"Error sending to {recipient}: {e}")
                    results['failed'] += 1
            
            # Delay between batches to avoid rate limits
            if i + batch_size < len(recipients):
                time.sleep(delay_between_batches)
        
        return results
    
    # ----- Email Monitoring -----
    
    def start_monitoring(self, check_interval: int = 300, folder: str = 'INBOX') -> None:
        """Start monitoring for new emails."""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._monitor_emails,
            args=(check_interval, folder),
            daemon=True
        )
        self._monitor_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop email monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
    
    def _monitor_emails(self, check_interval: int, folder: str) -> None:
        """Monitor emails in background thread."""
        processed_uids = set()
        
        while self._monitoring:
            try:
                # Check for new emails
                self.client.connect_imap()
                emails = self.client.fetch_recent_emails(count=10, folder=folder)
                
                for email in emails:
                    if email.uid not in processed_uids:
                        # Process new email
                        self._process_new_email(email)
                        processed_uids.add(email.uid)
                
                # Clean up old UIDs to prevent memory growth
                if len(processed_uids) > 1000:
                    processed_uids = set(list(processed_uids)[-500:])
                
            except Exception as e:
                print(f"Error in email monitoring: {e}")
            
            time.sleep(check_interval)
    
    def _process_new_email(self, email: ReceivedEmail) -> None:
        """Process a new email with auto-replies and filters."""
        # Check auto-reply rules
        for rule in self.auto_reply_rules:
            if rule.matches(email):
                self._send_auto_reply(email, rule)
                break
        
        # Apply filters
        for filter in self.filters:
            try:
                filter.apply(email, self.client)
            except Exception as e:
                print(f"Error applying filter {filter.name}: {e}")
    
    # ----- Common Automation Helpers -----
    
    @staticmethod
    def create_subject_filter(keyword: str) -> Callable[[ReceivedEmail], bool]:
        """Create a filter that matches emails with keyword in subject."""
        return lambda email: keyword.lower() in email.subject.lower()
    
    @staticmethod
    def create_sender_filter(sender: str) -> Callable[[ReceivedEmail], bool]:
        """Create a filter that matches emails from specific sender."""
        return lambda email: sender.lower() in email.from_addr.lower()
    
    @staticmethod
    def create_date_filter(days_old: int) -> Callable[[ReceivedEmail], bool]:
        """Create a filter that matches emails older than specified days."""
        return lambda email: (datetime.now() - email.date).days > days_old