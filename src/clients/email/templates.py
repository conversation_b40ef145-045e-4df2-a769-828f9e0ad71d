"""Email template system for common automation scenarios."""

from typing import Dict, Any, Optional
from dataclasses import dataclass
from string import Template
import json
from pathlib import Path


@dataclass
class EmailTemplate:
    """Email template with variable substitution."""
    name: str
    subject_template: str
    body_template: str
    html_template: Optional[str] = None
    default_vars: Optional[Dict[str, Any]] = None
    
    def render(self, **kwargs) -> Dict[str, str]:
        """Render template with provided variables."""
        # Merge default vars with provided vars
        vars_dict = self.default_vars.copy() if self.default_vars else {}
        vars_dict.update(kwargs)
        
        # Render templates
        subject = Template(self.subject_template).safe_substitute(vars_dict)
        body = Template(self.body_template).safe_substitute(vars_dict)
        
        result = {
            'subject': subject,
            'body': body
        }
        
        if self.html_template:
            result['html_body'] = Template(self.html_template).safe_substitute(vars_dict)
        
        return result


class TemplateManager:
    """Manage email templates."""
    
    def __init__(self):
        self.templates: Dict[str, EmailTemplate] = {}
        self._load_default_templates()
    
    def _load_default_templates(self):
        """Load default email templates."""
        # Welcome email
        self.add_template(EmailTemplate(
            name="welcome",
            subject_template="Welcome to $company_name!",
            body_template="""Dear $name,

Welcome to $company_name! We're excited to have you on board.

Your account has been successfully created. Here are your next steps:
1. Complete your profile
2. Explore our features
3. Connect with the team

If you have any questions, please don't hesitate to reach out.

Best regards,
The $company_name Team""",
            html_template="""<html>
<body>
<h2>Welcome to $company_name!</h2>
<p>Dear $name,</p>
<p>Welcome to <strong>$company_name</strong>! We're excited to have you on board.</p>
<p>Your account has been successfully created. Here are your next steps:</p>
<ol>
<li>Complete your profile</li>
<li>Explore our features</li>
<li>Connect with the team</li>
</ol>
<p>If you have any questions, please don't hesitate to reach out.</p>
<p>Best regards,<br>The $company_name Team</p>
</body>
</html>""",
            default_vars={"company_name": "Our Company"}
        ))
        
        # Status update
        self.add_template(EmailTemplate(
            name="status_update",
            subject_template="Project Status Update: $project_name",
            body_template="""Hello $name,

Here's the latest status update for $project_name:

Status: $status
Progress: $progress%
Last Updated: $update_date

Summary:
$summary

Next Steps:
$next_steps

Please let me know if you have any questions.

Best regards,
$sender_name""",
            default_vars={
                "status": "On Track",
                "progress": "50",
                "sender_name": "Project Manager"
            }
        ))
        
        # Task reminder
        self.add_template(EmailTemplate(
            name="task_reminder",
            subject_template="Reminder: $task_name due $due_date",
            body_template="""Hi $name,

This is a friendly reminder that the following task is due soon:

Task: $task_name
Due Date: $due_date
Priority: $priority
Description: $description

Please complete this task by the due date. If you need any assistance, feel free to reach out.

Thanks,
$sender_name""",
            default_vars={
                "priority": "Medium",
                "sender_name": "Task Manager"
            }
        ))
        
        # Meeting invitation
        self.add_template(EmailTemplate(
            name="meeting_invite",
            subject_template="Meeting Invitation: $meeting_title",
            body_template="""Dear $name,

You're invited to the following meeting:

Title: $meeting_title
Date: $meeting_date
Time: $meeting_time
Duration: $duration
Location: $location

Agenda:
$agenda

Please confirm your attendance.

Best regards,
$organizer_name""",
            html_template="""<html>
<body>
<h3>Meeting Invitation</h3>
<p>Dear $name,</p>
<p>You're invited to the following meeting:</p>
<table border="1" cellpadding="5">
<tr><td><strong>Title:</strong></td><td>$meeting_title</td></tr>
<tr><td><strong>Date:</strong></td><td>$meeting_date</td></tr>
<tr><td><strong>Time:</strong></td><td>$meeting_time</td></tr>
<tr><td><strong>Duration:</strong></td><td>$duration</td></tr>
<tr><td><strong>Location:</strong></td><td>$location</td></tr>
</table>
<p><strong>Agenda:</strong><br>$agenda</p>
<p>Please confirm your attendance.</p>
<p>Best regards,<br>$organizer_name</p>
</body>
</html>""",
            default_vars={
                "duration": "1 hour",
                "location": "Conference Room"
            }
        ))
        
        # Error notification
        self.add_template(EmailTemplate(
            name="error_notification",
            subject_template="[ALERT] Error in $system_name: $error_type",
            body_template="""SYSTEM ALERT

An error has occurred in $system_name:

Error Type: $error_type
Severity: $severity
Time: $error_time
Environment: $environment

Error Details:
$error_details

Stack Trace:
$stack_trace

Please investigate immediately.

This is an automated message from $system_name monitoring.""",
            default_vars={
                "severity": "High",
                "environment": "Production",
                "system_name": "System"
            }
        ))
    
    def add_template(self, template: EmailTemplate) -> None:
        """Add a template to the manager."""
        self.templates[template.name] = template
    
    def get_template(self, name: str) -> Optional[EmailTemplate]:
        """Get a template by name."""
        return self.templates.get(name)
    
    def list_templates(self) -> list[str]:
        """List all available template names."""
        return list(self.templates.keys())
    
    def render_template(self, name: str, **kwargs) -> Optional[Dict[str, str]]:
        """Render a template by name with variables."""
        template = self.get_template(name)
        if template:
            return template.render(**kwargs)
        return None
    
    def save_templates(self, file_path: Path) -> None:
        """Save templates to a JSON file."""
        data = {}
        for name, template in self.templates.items():
            data[name] = {
                'subject_template': template.subject_template,
                'body_template': template.body_template,
                'html_template': template.html_template,
                'default_vars': template.default_vars
            }
        
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def load_templates(self, file_path: Path) -> None:
        """Load templates from a JSON file."""
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        for name, template_data in data.items():
            self.add_template(EmailTemplate(
                name=name,
                subject_template=template_data['subject_template'],
                body_template=template_data['body_template'],
                html_template=template_data.get('html_template'),
                default_vars=template_data.get('default_vars')
            ))