# Instagram Browser Controller

A Python-based controller for automating Instagram interactions in a real browser using the Computer Use AI SDK MCP server.

## Features

🌐 **Browser Control**
- Open Instagram in any browser (Chrome, Arc, Safari, etc.)
- Navigate between different Instagram pages
- Real browser automation (no API limitations)

🔐 **Authentication**
- Login with username/password
- Automatic login state detection

🔍 **Search & Discovery**
- Search for users by username
- Search for hashtags
- Navigate to user profiles directly

❤️ **Content Interaction**
- Like posts
- Comment on posts
- Follow/unfollow users
- Scroll through feed

💬 **Messaging**
- Send direct messages
- Navigate to DM conversations

📖 **Stories**
- View Instagram stories
- Navigate through story content

🛠️ **Debugging Tools**
- View current page elements
- Get page information
- Interactive debugging mode

## Prerequisites

1. **MCP Server Running**: Make sure the Computer Use AI SDK MCP server is running:
   ```bash
   cd src/mcp-server-os-level
   cargo run --bin server
   ```

2. **Browser**: Have a supported browser installed (Chrome, Arc, Safari, etc.)

3. **Accessibility Permissions**: On macOS, ensure accessibility permissions are granted for the automation to work.

## Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Set up environment variables (optional, for auto-login):
   ```bash
   export INSTAGRAM_USERNAME="your_username"
   export INSTAGRAM_PASSWORD="your_password"
   ```

## Usage

### Interactive Mode

Run the controller in interactive mode:

```bash
python instagram_controller.py
```

This will show a menu with all available actions:

```
📱 Instagram Controller Menu:
1. Open Instagram
2. Login
3. Search user
4. Search hashtag
5. Like first post
6. Follow user (on profile page)
7. Send DM
8. Scroll feed
9. Navigate to profile
10. View stories
11. Comment on post
12. Show current page info
13. Show page elements (debug)
0. Exit
```

### Programmatic Usage

You can also use the controller programmatically:

```python
from instagram_controller import InstagramController, InstagramConfig

# Initialize controller
config = InstagramConfig(browser="Chrome")
controller = InstagramController(config)

# Open Instagram and login
controller.open_instagram()
controller.login("your_username", "your_password")

# Search for a user
controller.search("username", "user")

# Like the first post
controller.like_post()

# Navigate to a specific profile
controller.navigate_to_profile("username")

# Follow the user
controller.follow_user()

# Send a DM
controller.send_direct_message("Hello!")
```

## Configuration

The `InstagramConfig` class allows you to customize the controller:

```python
config = InstagramConfig(
    mcp_server_url="http://localhost:8080",  # MCP server URL
    browser="Chrome",                        # Browser to use
    instagram_url="https://www.instagram.com",
    wait_time=2.0,                          # Wait time between actions
    max_retries=3                           # Max retries for failed actions
)
```

## Supported Browsers

- **Chrome** (recommended)
- **Arc**
- **Safari**
- **Microsoft Edge**
- Any browser that supports the macOS accessibility API

## How It Works

The Instagram controller works by:

1. **Opening Instagram** in your real browser using the MCP server's `openUrl` function
2. **Reading page elements** using the accessibility API to find buttons, text fields, etc.
3. **Interacting with elements** by clicking, typing, and scrolling
4. **Waiting for page changes** and refreshing the element list

This approach provides:
- ✅ **Real browser behavior** - no detection as a bot
- ✅ **Full Instagram features** - access to all functionality
- ✅ **Visual feedback** - you can see what's happening
- ✅ **No API limitations** - works with Instagram's web interface

## Troubleshooting

### Common Issues

1. **"MCP request failed"**
   - Make sure the MCP server is running on `localhost:8080`
   - Check that the server started without errors

2. **"Could not find element"**
   - Instagram's UI may have changed
   - Try using the debug mode (option 13) to see available elements
   - Wait a bit longer for pages to load

3. **"Accessibility permissions"**
   - On macOS, grant accessibility permissions to your terminal/IDE
   - System Preferences > Security & Privacy > Privacy > Accessibility

4. **Login fails**
   - Check your username/password
   - Instagram may require 2FA or captcha verification
   - Try logging in manually first

### Debug Mode

Use option 13 in the interactive menu to see all page elements:

```
📋 Current page elements (showing first 20):
--------------------------------------------------------------------------------
  0: button       | submit       | Log in
  1: textbox      | text         | Phone number, username, or email
  2: textbox      | password     | Password
  3: link         |              | Forgotten your password?
...
```

This helps you understand what elements are available for interaction.

## Safety Notes

- 🚨 **Rate Limiting**: Instagram has rate limits. Don't perform actions too quickly.
- 🚨 **Terms of Service**: Make sure your usage complies with Instagram's ToS.
- 🚨 **Account Safety**: Use responsibly to avoid account restrictions.
- 🚨 **2FA**: The controller doesn't handle 2FA automatically.

## Contributing

Feel free to extend the controller with additional features:

- Story posting
- IGTV interactions
- Reels functionality
- Advanced search filters
- Bulk operations

## License

This project is part of the Computer Use AI SDK and follows the same license terms.
