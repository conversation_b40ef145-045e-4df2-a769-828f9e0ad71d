# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Computer Use AI SDK that provides an MCP (Model Context Protocol) server for controlling desktop applications through accessibility APIs. It consists of three main components:

1. **mcp-server-os-level** - Rust-based server providing OS-level automation
2. **mcp-client-cli-interface** - TypeScript CLI client for testing
3. **mcp-client-nextjs** - Next.js web interface client

## Common Development Tasks

### Building and Running the Server

```bash
# Build and run the Rust server
cd mcp-server-os-level
cargo run --bin server
# Keep this running in the background
```

### Running Tests

```bash
# Run Rust integration tests
cd mcp-server-os-level
cargo run --example test_get_all_apps
cargo run --example test_click_by_role
cargo run --example test_get_messages_and_send_message

# Run JavaScript MCP protocol tests
cd mcp-client-cli-interface
node tests/test-mcp.js
```

### CLI Client Setup

```bash
cd mcp-client-cli-interface
npm install
export ANTHROPIC_API_KEY=sk-ant-xxxx  # Required
npx tsx main.ts
```

### Next.js Client Setup

```bash
cd mcp-client-nextjs
npm install
echo "ANTHROPIC_API_KEY=sk-ant-xxxx" > .env
npm run dev     # Development server
npm run build   # Production build
npm run lint    # Run linting
```

## Architecture

### MCP Server Endpoints

The server exposes MCP protocol on `http://127.0.0.1:8080/mcp` with these key functions:

- `openApplication` - Launch applications
- `openUrl` - Open URLs in browsers
- `listInteractableElements` - Get UI elements
- `clickByIndex` - Click elements by index
- `typeByIndex` - Type text in elements
- `pressKeyByIndex` - Send key presses
- `scrollElement` - Scroll UI elements
- `inputControl` - General input control

### Platform Support

- Primary: macOS (full accessibility API support)
- Planned: Windows, Linux (stubs present)

### Key Dependencies

- **Rust**: tokio, axum, accessibility-sys (macOS)
- **TypeScript**: @modelcontextprotocol/sdk, @anthropic-ai/sdk
- **Next.js**: React 19, Tailwind CSS

## Important Notes

- The Rust server must be running before starting any client
- API key must be set as environment variable for clients
- macOS may require accessibility permissions for the server
- The server uses native accessibility APIs, not pixel-based automation