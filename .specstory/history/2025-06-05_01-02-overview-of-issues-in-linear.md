<!-- Generated by SpecStory -->

# Overview of issues in Linear (2025-06-04 21:02:57)

_**User**_

Please build comprehensive overview of all issues in Linear

---

_**Assistant**_



---

Here is a comprehensive overview of all issues currently in Linear, grouped by project and milestone, with key details for each:

---

## 1. LLM-Powered Spreadsheet Processor

### [OVERVIEW] Milestone Roadmap ([AI-54](https://linear.app/invisible-ai-rnd/issue/AI-54/overview-llm-powered-spreadsheet-processor-milestone-roadmap))
- **Summary:** End-to-end AI-powered spreadsheet processor (Excel/Google Sheets) with LLM and Python function execution, real-time sync, and robust configuration.
- **Milestones:** Core Foundation, Data Layer, Configuration & UX, Quality & Release

---

### Milestone 1: Core Foundation (Weeks 1-4)
- **[AI-30] Epic 1: Core Processing Engine** ([link](https://linear.app/invisible-ai-rnd/issue/AI-30/m1-epic-1-core-processing-engine))  
  - Template variable substitution, LLM prompt execution, Python function interpreter, parallel processing.
- **[AI-31] Implement Template Variable Substitution** ([link](https://linear.app/invisible-ai-rnd/issue/AI-31/m11-implement-template-variable-substitution))
- **[AI-32] Build LLM Prompt Execution Engine** ([link](https://linear.app/invisible-ai-rnd/issue/AI-32/m12-build-llm-prompt-execution-engine))
- **[AI-33] Implement Python Function Interpreter** ([link](https://linear.app/invisible-ai-rnd/issue/AI-33/implement-python-function-interpreter))
- **[AI-34] Optimize Parallel Processing** ([link](https://linear.app/invisible-ai-rnd/issue/AI-34/optimize-parallel-processing))

---

### Milestone 2: Data Layer (Weeks 5-8)
- **[AI-35] Epic 2: Data Integration** ([link](https://linear.app/invisible-ai-rnd/issue/AI-35/m2-epic-2-data-integration))  
  - Google Sheets, Excel/CSV, real-time sync, backup/versioning.
- **[AI-36] Integrate Google Sheets API** ([link](https://linear.app/invisible-ai-rnd/issue/AI-36/m21-integrate-google-sheets-api))
- **[AI-37] Implement Excel/CSV File Handler** ([link](https://linear.app/invisible-ai-rnd/issue/AI-37/implement-excelcsv-file-handler))
- **[AI-38] Build Real-time Sync Mechanism** ([link](https://linear.app/invisible-ai-rnd/issue/AI-38/build-real-time-sync-mechanism))
- **[AI-39] Implement Automatic Backup System** ([link](https://linear.app/invisible-ai-rnd/issue/AI-39/implement-automatic-backup-system))

---

### Milestone 3: Configuration & UX (Weeks 9-11)
- **[AI-40] Epic 3: Configuration & Validation** ([link](https://linear.app/invisible-ai-rnd/issue/AI-40/m3-epic-3-configuration-and-validation))
- **[AI-41] Build Per-Column Model Configuration** ([link](https://linear.app/invisible-ai-rnd/issue/AI-41/m31-build-per-column-model-configuration))
- **[AI-42] Create Input Validation Framework** ([link](https://linear.app/invisible-ai-rnd/issue/AI-42/create-input-validation-framework))
- **[AI-43] Implement Column Matching with Fuzzy Logic** ([link](https://linear.app/invisible-ai-rnd/issue/AI-43/implement-column-matching-with-fuzzy-logic))
- **[AI-44] Epic 4: User Experience** ([link](https://linear.app/invisible-ai-rnd/issue/AI-44/m3-epic-4-user-experience))
- **[AI-45] Build Rich CLI Interface** ([link](https://linear.app/invisible-ai-rnd/issue/AI-45/build-rich-cli-interface))
- **[AI-46] Implement Progress Tracking** ([link](https://linear.app/invisible-ai-rnd/issue/AI-46/implement-progress-tracking))
- **[AI-47] Add Debug Mode and Logging** ([link](https://linear.app/invisible-ai-rnd/issue/AI-47/add-debug-mode-and-logging))
- **[AI-48] Build Error Reporting and Recovery** ([link](https://linear.app/invisible-ai-rnd/issue/AI-48/build-error-reporting-and-recovery))

---

### Milestone 4: Quality & Release (Weeks 12-14)
- **[AI-49] Epic 5: Testing & Quality** ([link](https://linear.app/invisible-ai-rnd/issue/AI-49/m4-epic-5-testing-and-quality))
- **[AI-50] Create Unit Test Suite** ([link](https://linear.app/invisible-ai-rnd/issue/AI-50/m41-create-unit-test-suite))
- **[AI-51] Build Integration Test Framework** ([link](https://linear.app/invisible-ai-rnd/issue/AI-51/build-integration-test-framework))
- **[AI-52] Setup Performance Benchmarking** ([link](https://linear.app/invisible-ai-rnd/issue/AI-52/setup-performance-benchmarking))
- **[AI-53] Configure CI/CD Pipeline** ([link](https://linear.app/invisible-ai-rnd/issue/AI-53/configure-cicd-pipeline))

---

## 2. Task Prioritisation Assistant

- **[AI-21] LLM and architecture subagent** ([link](https://linear.app/invisible-ai-rnd/issue/AI-21/llm-and-architecture-subagent)) – In Progress
- **[AI-19] ScreenPipe and screen capture subagent** ([link](https://linear.app/invisible-ai-rnd/issue/AI-19/screenpipe-and-screen-capture-subagent))
- **[AI-20] Task management integration subagent** ([link](https://linear.app/invisible-ai-rnd/issue/AI-20/task-management-integration-subagent))
- **[AI-22] macOS APIs and interventions subagent** ([link](https://linear.app/invisible-ai-rnd/issue/AI-22/macos-apis-and-interventions-subagent))
- **[AI-23] Privacy and existing tools subagent** ([link](https://linear.app/invisible-ai-rnd/issue/AI-23/privacy-and-existing-tools-subagent))

---

## 3. Apollo prompt tasks

- **[AI-29] Data Check auditor** ([link](https://linear.app/invisible-ai-rnd/issue/AI-29/data-check-auditor)) – Backlog
- **[AI-26] Debt Doc extractor** ([link](https://linear.app/invisible-ai-rnd/issue/AI-26/debt-doc-extractor)) – Backlog
- **[AI-28] DD Question list enhancements** ([link](https://linear.app/invisible-ai-rnd/issue/AI-28/dd-question-list-enhancements)) – Backlog
- **[AI-27] Bid letter enhancements** ([link](https://linear.app/invisible-ai-rnd/issue/AI-27/bid-letter-enhancements)) – Backlog
- **[AI-25] Company 101** ([link](https://linear.app/invisible-ai-rnd/issue/AI-25/company-101)) – Backlog
- **[AI-24] Industry 101** ([link](https://linear.app/invisible-ai-rnd/issue/AI-24/industry-101)) – Backlog

---

## 4. llm-agent-excel (excel-as-ui)

- **[AI-15] Small model Number Checking** – Done
- **[AI-10] Delegates (arbitrary python) functions callable** – In Progress
- **[AI-14] Claim Extraction** – In Progress
- **[AI-16] Sentinels / Accuracy Probes (probe swarm? :P)** – In Progress
- **[AI-17] 4.1-nano Number checkers** – In Progress
- **[AI-13] Agreed Base Template Output Format (glob var)** – In Progress
- **[AI-12] Accuracy Estimations** – In Progress
- **[AI-11] Add Footnote Format (transcript 1) + [1]** – In Progress
- **[AI-18] Deck Builder** – Backlog

---

## 5. Welcome Tasks (Linear onboarding)

- **[WEL-1] Welcome to Linear 👋**
- **[WEL-2] 3 ways to navigate Linear: Command menu, keyboard or mouse**
- **[WEL-3] Connect GitHub or GitLab**
- **[WEL-4] Customize settings**
- **[WEL-5] Connect to Slack**
- **[WEL-6] Next steps**
- **[WEL-7] Use Projects to organize work for features or releases**
- **[WEL-8] Invite your teammates**
- **[WEL-9] Use Cycles to focus work over n–weeks**

---

### Status Key
- **Todo:** Not started
- **In Progress:** Work ongoing
- **Backlog:** Planned, not started
- **Done:** Completed

---

### Highlights & Urgent Issues
- **Urgent:** [AI-55] Milestone Progress Tracker, [AI-35] Data Integration, [AI-30] Core Processing Engine, [AI-54] Project Overview
- **In Progress:** [AI-21], [AI-10], [AI-14], [AI-16], [AI-17], [AI-13], [AI-12], [AI-11]
- **Done:** [AI-15]

---

If you want a CSV, markdown table, or filtered view (e.g., only urgent, only in progress, etc.), let me know!

---

_**User**_

List all issues not attached to milestones, then suggest which they likely should be attached to and whether any milestones are missing.

---

_**Assistant**_

Here are all issues that are **not attached to a milestone** (i.e., not referenced in any milestone/epic/roadmap issue):

---

### Issues Not Attached to Any Milestone

#### **Task Prioritisation Assistant**
- [AI-21] LLM and architecture subagent  
  *Research LLM integration strategies for screen analysis and overall system architecture patterns*  
  **Status:** In Progress

- [AI-19] ScreenPipe and screen capture subagent  
  *Research ScreenPipe capabilities, macOS screen capture APIs, and metadata extraction methods*  
  **Status:** Todo

- [AI-20] Task management integration subagent  
  *Research which task management apps have good APIs and how to integrate with them*  
  **Status:** Todo

- [AI-22] macOS APIs and interventions subagent  
  *Research notification APIs, window management APIs, and implementation of escalating interventions*  
  **Status:** Todo

- [AI-23] Privacy and existing tools subagent  
  *Research privacy/security considerations and similar existing tools*  
  **Status:** Todo

#### **Apollo prompt tasks**
- [AI-29] Data Check auditor  
  **Status:** Backlog

- [AI-26] Debt Doc extractor  
  **Status:** Backlog

- [AI-28] DD Question list enhancements  
  **Status:** Backlog

- [AI-27] Bid letter enhancements  
  **Status:** Backlog

- [AI-25] Company 101  
  **Status:** Backlog

- [AI-24] Industry 101  
  **Status:** Backlog

#### **llm-agent-excel (excel-as-ui)**
- [AI-15] Small model Number Checking  
  **Status:** Done

- [AI-10] Delegates (arbitrary python) functions callable  
  **Status:** In Progress

- [AI-14] Claim Extraction  
  **Status:** In Progress

- [AI-16] Sentinels / Accuracy Probes (probe swarm? :P)  
  **Status:** In Progress

- [AI-17] 4.1-nano Number checkers  
  **Status:** In Progress

- [AI-13] Agreed Base Template Output Format (glob var)  
  **Status:** In Progress

- [AI-12] Accuracy Estimations  
  **Status:** In Progress

- [AI-11] Add Footnote Format (transcript 1) + [1]  
  **Status:** In Progress

- [AI-18] Deck Builder  
  **Status:** Backlog

#### **Welcome Tasks (Linear onboarding)**
- [WEL-1] Welcome to Linear 👋
- [WEL-2] 3 ways to navigate Linear: Command menu, keyboard or mouse
- [WEL-3] Connect GitHub or GitLab
- [WEL-4] Customize settings
- [WEL-5] Connect to Slack
- [WEL-6] Next steps
- [WEL-7] Use Projects to organize work for features or releases
- [WEL-8] Invite your teammates
- [WEL-9] Use Cycles to focus work over n–weeks

---

## Suggestions: Where Should These Issues Be Attached?

### 1. **Task Prioritisation Assistant**
- These are all research/subagent issues.  
  **Suggestion:**  
  - Create a new milestone/epic: **"Task Prioritisation Assistant: Subagent Research & Integrations"**  
  - Attach all [AI-19], [AI-20], [AI-21], [AI-22], [AI-23] to this milestone.

### 2. **Apollo prompt tasks**
- These are all related to prompt engineering, data checks, and document extraction.  
  **Suggestion:**  
  - Create a milestone/epic: **"Apollo Prompt Tasks: Data & Document Enhancements"**  
  - Attach [AI-24], [AI-25], [AI-26], [AI-27], [AI-28], [AI-29] to this milestone.

### 3. **llm-agent-excel (excel-as-ui)**
- These are all feature or research issues for the Excel agent.  
  **Suggestion:**  
  - Create a milestone/epic: **"Excel Agent: Core Features & Accuracy"**  
  - Attach [AI-10], [AI-11], [AI-12], [AI-13], [AI-14], [AI-15], [AI-16], [AI-17], [AI-18] to this milestone.

### 4. **Welcome Tasks**
- These are onboarding/tutorial issues for new users.  
  **Suggestion:**  
  - These are intentionally not attached to product milestones; no action needed unless you want to group them under a "Linear Onboarding" milestone for tracking.

---

## Are Any Milestones Missing?

**Yes.**  
- There are no explicit milestones/epics for:
  - Task Prioritisation Assistant (subagent research/integration)
  - Apollo prompt tasks (data/document enhancements)
  - llm-agent-excel (excel-as-ui) (core features, accuracy, and research)
  - Welcome Tasks (onboarding)

**Recommendation:**  
- Create the above milestones/epics to ensure all issues are tracked under a clear roadmap.
- This will improve visibility, progress tracking, and prioritization.

---

Would you like a markdown table or CSV of these suggestions, or help with milestone/epic descriptions?

---

_**User**_

do it. Add _csr to anything you create so I know it was from you.

---

_**Assistant**_



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



---



All missing milestones have now been created (with `_csr` suffix) and all previously unassigned issues have been attached to the appropriate new milestone:

### Created Milestones/Epics
- **Task Prioritisation Assistant: Subagent Research & Integrations_csr**
- **Apollo Prompt Tasks: Data & Document Enhancements_csr**
- **Excel Agent: Core Features & Accuracy_csr**
- **Linear Onboarding_csr**

### All previously unassigned issues are now attached to these milestones, including:
- All Task Prioritisation Assistant subagent/research issues
- All Apollo prompt tasks (data/doc enhancements)
- All llm-agent-excel (excel-as-ui) core/accuracy/research issues
- All Welcome Tasks (onboarding/tutorials)

**You can now easily track these issues by their new milestone/epic.**

Would you like a summary table, a CSV export, or any further grouping/filtering?

---

_**User**_

OK. Can we now make some kind of visual overview of all tasks and how they all fit together into the larger plan please?

---

_**Assistant**_

<details>
            <summary>Searched codebase "mermaid diagram or gantt or roadmap or dependency graph" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `AGENT_FACTORY_INDEX.md` | L20-41 |
| `AGENT_FACTORY_INDEX.md` | L71-79 |
| `AGENT_FACTORY_INDEX.md` | L41-71 |
| `AGENT_FACTORY_INDEX.md` | L1-20 |
| `CLAUDE.md` | L1-80 |
| `README.md` | L1-63 |
| `CLAUDE.md` | L80-89 |
| `mcp-server-os-level/Cargo.toml` | L1-52 |
| `README.md` | L63-96 |
| `mcp-client-cli-interface/package.json` | L1-24 |
| `mcp-client-cli-interface/README.md` | L1-1 |
| `mcp-client-nextjs/src/lib/mcp/start-here.ts` | L1-49 |
| `mcp-client-cli-interface/start-here.ts` | L1-28 |
| `mcp-client-nextjs/.gitignore` | L1-43 |
| `mcp-client-nextjs/README.md` | L1-1 |
| `mcp-client-cli-interface/query-processing-engine.ts` | L247-301 |
| `mcp-client-nextjs/src/app/layout.tsx` | L1-18 |
| `.gitignore` | L1-41 |
| `mcp-client-cli-interface/query-processing-engine.ts` | L301-347 |
| `mcp-client-nextjs/src/components/mcp-interface.tsx` | L308-350 |
| `mcp-client-nextjs/src/components/mcp-interface.tsx` | L1-52 |
| `mcp-client-cli-interface/start-here.ts` | L81-108 |
| `mcp-client-nextjs/src/lib/mcp/query-processing-engine.ts` | L250-306 |
| `mcp-client-nextjs/src/lib/mcp/query-processing-engine.ts` | L306-350 |
| `mcp-client-nextjs/package.json` | L1-33 |

</details>

---



---

Here is a visual overview of all tasks and how they fit together into the larger plan, using a hierarchical flowchart. Each major project/milestone is shown as a group, with tasks as nodes. Cross-project relationships (dotted lines) illustrate key dependencies or shared concepts.

**Legend:**
- Each colored group is a project or epic/milestone.
- Arrows show which tasks belong to which milestone.
- Dotted arrows show important cross-project relationships (e.g., shared logic, dependencies).

---

**Diagram:**  
(You can view this as a Mermaid diagram in any compatible Markdown viewer, or let me know if you want a PNG/SVG export.)

```mermaid
flowchart TD
  subgraph LLM-Powered Spreadsheet Processor
    direction TB
    A1([AI-54 OVERVIEW])
    A2([AI-30 Core Processing Engine])
    A3([AI-35 Data Integration])
    A4([AI-40 Config & Validation])
    A5([AI-44 User Experience])
    A6([AI-49 Testing & Quality])
    A2a([AI-31 Template Substitution])
    A2b([AI-32 LLM Prompt Engine])
    A2c([AI-33 Python Interpreter])
    A2d([AI-34 Parallel Processing])
    A3a([AI-36 Google Sheets API])
    A3b([AI-37 Excel/CSV Handler])
    A3c([AI-38 Real-time Sync])
    A3d([AI-39 Backup System])
    A4a([AI-41 Per-Column Config])
    A4b([AI-42 Input Validation])
    A4c([AI-43 Fuzzy Column Matching])
    A5a([AI-45 CLI Interface])
    A5b([AI-46 Progress Tracking])
    A5c([AI-47 Debug & Logging])
    A5d([AI-48 Error Recovery])
    A6a([AI-50 Unit Tests])
    A6b([AI-51 Integration Tests])
    A6c([AI-52 Benchmarking])
    A6d([AI-53 CI/CD])
    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5
    A1 --> A6
    A2 --> A2a
    A2 --> A2b
    A2 --> A2c
    A2 --> A2d
    A3 --> A3a
    A3 --> A3b
    A3 --> A3c
    A3 --> A3d
    A4 --> A4a
    A4 --> A4b
    A4 --> A4c
    A5 --> A5a
    A5 --> A5b
    A5 --> A5c
    A5 --> A5d
    A6 --> A6a
    A6 --> A6b
    A6 --> A6c
    A6 --> A6d
  end
  subgraph TaskPrioritisationAssistant
    direction TB
    B1([AI-56 Subagent Research & Integrations_csr])
    B2([AI-21 LLM & Architecture])
    B3([AI-19 ScreenPipe])
    B4([AI-20 Task Mgmt Integration])
    B5([AI-22 macOS APIs])
    B6([AI-23 Privacy & Tools])
    B1 --> B2
    B1 --> B3
    B1 --> B4
    B1 --> B5
    B1 --> B6
  end
  subgraph ApolloPromptTasks
    direction TB
    C1([AI-57 Data & Doc Enhancements_csr])
    C2([AI-24 Industry 101])
    C3([AI-25 Company 101])
    C4([AI-26 Debt Doc Extractor])
    C5([AI-27 Bid Letter Enhancements])
    C6([AI-28 DD Q Enhancements])
    C7([AI-29 Data Check Auditor])
    C1 --> C2
    C1 --> C3
    C1 --> C4
    C1 --> C5
    C1 --> C6
    C1 --> C7
  end
  subgraph ExcelAgent
    direction TB
    D1([AI-58 Core Features & Accuracy_csr])
    D2([AI-10 Delegates])
    D3([AI-11 Footnote Format])
    D4([AI-12 Accuracy Estimations])
    D5([AI-13 Template Output Format])
    D6([AI-14 Claim Extraction])
    D7([AI-15 Small Model Number Check])
    D8([AI-16 Sentinels/Probes])
    D9([AI-17 4.1-nano Number Checkers])
    D10([AI-18 Deck Builder])
    D1 --> D2
    D1 --> D3
    D1 --> D4
    D1 --> D5
    D1 --> D6
    D1 --> D7
    D1 --> D8
    D1 --> D9
    D1 --> D10
  end
  subgraph WelcomeTasks
    direction TB
    E1([WEL-10 Linear Onboarding_csr])
    E2([WEL-1 Welcome])
    E3([WEL-2 Navigation])
    E4([WEL-3 GitHub/GitLab])
    E5([WEL-4 Settings])
    E6([WEL-5 Slack])
    E7([WEL-6 Next Steps])
    E8([WEL-7 Projects])
    E9([WEL-8 Invite])
    E10([WEL-9 Cycles])
    E1 --> E2
    E1 --> E3
    E1 --> E4
    E1 --> E5
    E1 --> E6
    E1 --> E7
    E1 --> E8
    E1 --> E9
    E1 --> E10
  end
  %% Cross-project relationships (example, can be extended)
  A3a -.-> D4
  A2b -.-> D2
  B2 -.-> A2b
  C7 -.-> D4
  D6 -.-> A2b
```

---

**How to use this:**
- Paste the above into a Mermaid live editor (e.g., https://mermaid.live/) or a Markdown viewer with Mermaid support.
- You can extend the cross-project dotted lines to show more dependencies as needed.

Would you like a PNG/SVG export, or a more detailed breakdown of dependencies?

---

_**User**_

I think use an ascii tree diagram instead. This is too small to read.

---

_**Assistant**_

Here’s a clear, readable ASCII tree diagram showing how all tasks fit into the larger plan. Each milestone/epic is a branch, with its tasks as leaves. Cross-project dependencies are noted at the end.

```
LLM-Powered Spreadsheet Processor
|
|-- [AI-54] OVERVIEW
    |-- [AI-30] Core Processing Engine
    |   |-- [AI-31] Template Substitution
    |   |-- [AI-32] LLM Prompt Engine
    |   |-- [AI-33] Python Interpreter
    |   |-- [AI-34] Parallel Processing
    |
    |-- [AI-35] Data Integration
    |   |-- [AI-36] Google Sheets API
    |   |-- [AI-37] Excel/CSV Handler
    |   |-- [AI-38] Real-time Sync
    |   |-- [AI-39] Backup System
    |
    |-- [AI-40] Config & Validation
    |   |-- [AI-41] Per-Column Config
    |   |-- [AI-42] Input Validation
    |   |-- [AI-43] Fuzzy Column Matching
    |
    |-- [AI-44] User Experience
    |   |-- [AI-45] CLI Interface
    |   |-- [AI-46] Progress Tracking
    |   |-- [AI-47] Debug & Logging
    |   |-- [AI-48] Error Recovery
    |
    |-- [AI-49] Testing & Quality

---

